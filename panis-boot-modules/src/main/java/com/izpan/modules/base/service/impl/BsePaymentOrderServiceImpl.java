/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import com.izpan.modules.base.domain.bo.BsePaymentOrderBO;
import com.izpan.modules.base.domain.entity.BsePaymentOrder;
import com.izpan.modules.base.repository.mapper.BsePaymentOrderMapper;
import com.izpan.modules.base.service.IBsePaymentOrderService;
import lombok.extern.slf4j.Slf4j;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.izpan.infrastructure.page.PageQuery;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 基础服务-支付与退款订单表 Service 服务接口实现层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.BsePaymentOrderServiceImpl
 * @CreateTime 2025-07-20 - 18:05:44
 */

@Service
public class BsePaymentOrderServiceImpl extends ServiceImpl<BsePaymentOrderMapper, BsePaymentOrder> implements IBsePaymentOrderService {

    @Override
    public IPage<BsePaymentOrder> listBsePaymentOrderPage(PageQuery pageQuery, BsePaymentOrderBO bsePaymentOrderBO) {
        LambdaQueryWrapper<BsePaymentOrder> queryWrapper = new LambdaQueryWrapper<BsePaymentOrder>()
            .eq(ObjectUtils.isNotEmpty(bsePaymentOrderBO.getOrderNo()), BsePaymentOrder::getOrderNo, bsePaymentOrderBO.getOrderNo())
            .eq(ObjectUtils.isNotEmpty(bsePaymentOrderBO.getStatus()), BsePaymentOrder::getStatus, bsePaymentOrderBO.getStatus()).orderByDesc(BsePaymentOrder::getCreateTime);
        return baseMapper.selectPage(pageQuery.buildPage(), queryWrapper);
    }

    @Override
    public boolean updateOrderPayStatus(String orderNo) {
        return false;
    }

}

