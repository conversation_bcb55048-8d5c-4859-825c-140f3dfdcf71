/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.izpan.infrastructure.config.WechatPayProperties;
import com.izpan.infrastructure.constants.WechatPayConstants;
import com.izpan.infrastructure.util.WechatPayUtil;
import com.izpan.modules.base.domain.dto.payment.WechatCallbackDTO;
import com.izpan.modules.base.domain.dto.payment.WechatPayOrderDTO;
import com.izpan.modules.base.domain.dto.payment.WechatRefundDTO;
import com.izpan.modules.base.domain.entity.BsePaymentOrder;
import com.izpan.modules.base.domain.vo.WechatPayResultVO;
import com.izpan.modules.base.service.IBsePaymentOrderService;
import com.izpan.modules.base.service.IWechatPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付服务实现类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.WechatPayServiceImpl
 * @CreateTime 2025-07-20
 */
@Slf4j
@Service
public class WechatPayServiceImpl implements IWechatPayService {

    private final WechatPayProperties wechatPayProperties;
    private final IBsePaymentOrderService bsePaymentOrderService;
    private final RestTemplate restTemplate;
    private final RestTemplate sslRestTemplate;

    public WechatPayServiceImpl(WechatPayProperties wechatPayProperties,
                               IBsePaymentOrderService bsePaymentOrderService,
                               RestTemplate restTemplate,
                               @Qualifier("sslRestTemplate") RestTemplate sslRestTemplate) {
        this.wechatPayProperties = wechatPayProperties;
        this.bsePaymentOrderService = bsePaymentOrderService;
        this.restTemplate = restTemplate;
        this.sslRestTemplate = sslRestTemplate;

        log.info("WechatPayServiceImpl初始化完成，支持SSL证书退款功能");
        log.info("普通RestTemplate类型: {}", restTemplate.getClass().getSimpleName());
        log.info("SSL RestTemplate类型: {}", sslRestTemplate.getClass().getSimpleName());
        log.info("SSL RestTemplate是否与普通RestTemplate相同: {}", restTemplate == sslRestTemplate);
    }

    @Override
    @Transactional
    public WechatPayResultVO createPayOrder(WechatPayOrderDTO wechatPayOrderDTO) {
        WechatPayResultVO result = new WechatPayResultVO();
        
        try {
            // 1. 生成商户订单号
            String orderNo = generateOrderNo();
            
            // 2. 创建支付订单记录
            BsePaymentOrder paymentOrder = createPaymentOrderRecord(wechatPayOrderDTO, orderNo);
            Long orderId = paymentOrder.getId();
            
            // 3. 调用微信统一下单接口
            String prepayId = callWechatUnifiedOrder(wechatPayOrderDTO, orderNo);
            
            if (StrUtil.isBlank(prepayId)) {
                result.setStatus(WechatPayConstants.ORDER_STATUS_FAILED);
                result.setErrorMsg("调用微信支付接口失败");
                return result;
            }
            
            // 4. 生成小程序支付参数
            Map<String, Object> payParams = WechatPayUtil.generateMiniProgramPayParams(
                wechatPayProperties.getAppId(), prepayId, wechatPayProperties.getApiKey());
            
            // 5. 返回结果
            result.setOrderNo(orderNo);
            result.setOrderId(orderId);
            result.setPayParams(payParams);
            result.setStatus(WechatPayConstants.ORDER_STATUS_PENDING);
            
            log.info("创建支付订单成功，订单号：{}", orderNo);
            
        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            result.setStatus(WechatPayConstants.ORDER_STATUS_FAILED);
            result.setErrorMsg("创建支付订单失败：" + e.getMessage());
        }
        
        return result;
    }

    @Override
    public String queryOrderStatus(String orderNo) {
        try {
            // 1. 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", wechatPayProperties.getAppId());
            params.put("mch_id", wechatPayProperties.getMchId());
            params.put("out_trade_no", orderNo);
            params.put("nonce_str", WechatPayUtil.generateNonceStr());
            
            // 2. 生成签名
            String sign = WechatPayUtil.generateSign(params, wechatPayProperties.getApiKey());
            params.put("sign", sign);
            
            // 3. 发送请求
            String xmlData = WechatPayUtil.mapToXml(params);
            String response = WechatPayUtil.sendPostRequest(WechatPayConstants.ORDER_QUERY_URL, xmlData, restTemplate);
            
            // 4. 解析响应
            Map<String, Object> responseMap = WechatPayUtil.xmlToMap(response);
            
            if (WechatPayConstants.WECHAT_PAY_SUCCESS.equals(responseMap.get("return_code")) &&
                WechatPayConstants.WECHAT_PAY_SUCCESS.equals(responseMap.get("result_code"))) {
                
                String tradeState = (String) responseMap.get("trade_state");
                return convertWechatStatusToOrderStatus(tradeState);
            }
            
        } catch (Exception e) {
            log.error("查询订单状态失败，订单号：{}", orderNo, e);
        }
        
        return WechatPayConstants.ORDER_STATUS_FAILED;
    }

    /**
     * 生成商户订单号
     */
    private String generateOrderNo() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return "WX" + LocalDateTime.now().format(formatter) + IdUtil.randomUUID().substring(0, 8);
    }

    /**
     * 创建支付订单记录
     */
    private BsePaymentOrder createPaymentOrderRecord(WechatPayOrderDTO dto, String orderNo) {
        BsePaymentOrder paymentOrder = new BsePaymentOrder();
        paymentOrder.setOrderNo(orderNo);
        paymentOrder.setUserId(dto.getUserId());
        paymentOrder.setPackageId(dto.getPackageId());
        paymentOrder.setPackageSnapshot(dto.getPackageSnapshot());
        paymentOrder.setAmountPayable(dto.getAmountPayable());
        paymentOrder.setStatus(WechatPayConstants.ORDER_STATUS_PENDING);
        paymentOrder.setPaymentChannel(WechatPayConstants.PAYMENT_CHANNEL_WECHAT_LITE);
        paymentOrder.setRefundStatus(WechatPayConstants.REFUND_STATUS_NO_REFUND);
        paymentOrder.setExpiresAt(LocalDateTime.now().plusMinutes(dto.getExpireMinutes()));
        
        bsePaymentOrderService.save(paymentOrder);
        return paymentOrder;
    }

    /**
     * 调用微信统一下单接口
     */
    private String callWechatUnifiedOrder(WechatPayOrderDTO dto, String orderNo) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", wechatPayProperties.getAppId());
            params.put("mch_id", wechatPayProperties.getMchId());
            params.put("nonce_str", WechatPayUtil.generateNonceStr());
            params.put("body", dto.getBody());
            params.put("out_trade_no", orderNo);
            params.put("total_fee", dto.getAmountPayable());
            params.put("spbill_create_ip", "127.0.0.1");
            params.put("notify_url", wechatPayProperties.getNotifyUrl());
            params.put("trade_type", WechatPayConstants.TRADE_TYPE_JSAPI);
            params.put("openid", dto.getOpenid());
            
            // 生成签名
            String sign = WechatPayUtil.generateSign(params, wechatPayProperties.getApiKey());
            params.put("sign", sign);
            
            // 发送请求
            String xmlData = WechatPayUtil.mapToXml(params);
            String response = WechatPayUtil.sendPostRequest(WechatPayConstants.UNIFIED_ORDER_URL, xmlData, restTemplate);
            
            // 解析响应
            Map<String, Object> responseMap = WechatPayUtil.xmlToMap(response);
            
            if (WechatPayConstants.WECHAT_PAY_SUCCESS.equals(responseMap.get("return_code")) &&
                WechatPayConstants.WECHAT_PAY_SUCCESS.equals(responseMap.get("result_code"))) {
                return (String) responseMap.get("prepay_id");
            } else {
                log.error("微信统一下单失败：{}", responseMap);
                return null;
            }
            
        } catch (Exception e) {
            log.error("调用微信统一下单接口失败", e);
            return null;
        }
    }

    /**
     * 转换微信支付状态为订单状态
     */
    private String convertWechatStatusToOrderStatus(String wechatStatus) {
        return switch (wechatStatus) {
            case "SUCCESS" -> WechatPayConstants.ORDER_STATUS_PAID;
            case "CLOSED" -> WechatPayConstants.ORDER_STATUS_CLOSED;
            case "PAYERROR" -> WechatPayConstants.ORDER_STATUS_FAILED;
            default -> WechatPayConstants.ORDER_STATUS_PENDING;
        };
    }

    @Override
    @Transactional
    public boolean handlePayCallback(Map<String, Object> callbackData) {
        try {
            log.info("开始处理微信支付回调，数据：{}", callbackData);

            // 1. 验证签名
            if (!WechatPayUtil.verifySign(callbackData, wechatPayProperties.getApiKey())) {
                log.error("微信支付回调签名验证失败，回调数据：{}", callbackData);
                return false;
            }

            // 2. 检查支付结果
            String returnCode = (String) callbackData.get("return_code");
            String resultCode = (String) callbackData.get("result_code");

            if (!WechatPayConstants.WECHAT_PAY_SUCCESS.equals(returnCode) ||
                !WechatPayConstants.WECHAT_PAY_SUCCESS.equals(resultCode)) {
                log.error("微信支付回调失败，return_code：{}，result_code：{}，错误信息：{}",
                    returnCode, resultCode, callbackData.get("err_code_des"));
                return false;
            }

            // 3. 更新订单状态
            return updateOrderAfterPayment(callbackData);

        } catch (Exception e) {
            log.error("处理微信支付回调失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean applyRefund(WechatRefundDTO wechatRefundDTO) {
        try {
            // 1. 查询订单信息
            BsePaymentOrder paymentOrder = bsePaymentOrderService.getById(wechatRefundDTO.getOrderId());
            if (paymentOrder == null || !WechatPayConstants.ORDER_STATUS_PAID.equals(paymentOrder.getStatus())) {
                log.error("订单不存在或状态不正确，无法退款");
                return false;
            }

            // 2. 生成退款单号
            String refundNo = generateRefundNo();

            // 3. 调用微信退款接口
            boolean refundResult = callWechatRefund(paymentOrder, wechatRefundDTO, refundNo);

            if (refundResult) {
                // 4. 更新订单退款状态
                paymentOrder.setRefundStatus(WechatPayConstants.REFUND_STATUS_PENDING);
                paymentOrder.setRefundNo(refundNo);
                paymentOrder.setRefundAmount(wechatRefundDTO.getRefundAmount());
                bsePaymentOrderService.updateById(paymentOrder);
            }

            return refundResult;

        } catch (Exception e) {
            log.error("申请退款失败", e);
            return false;
        }
    }

    @Override
    public String queryRefundStatus(String refundNo) {
        try {
            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", wechatPayProperties.getAppId());
            params.put("mch_id", wechatPayProperties.getMchId());
            params.put("out_refund_no", refundNo);
            params.put("nonce_str", WechatPayUtil.generateNonceStr());

            // 生成签名
            String sign = WechatPayUtil.generateSign(params, wechatPayProperties.getApiKey());
            params.put("sign", sign);

            // 发送请求
            String xmlData = WechatPayUtil.mapToXml(params);
            String response = WechatPayUtil.sendPostRequest(WechatPayConstants.REFUND_QUERY_URL, xmlData, restTemplate);

            // 解析响应
            Map<String, Object> responseMap = WechatPayUtil.xmlToMap(response);

            if (WechatPayConstants.WECHAT_PAY_SUCCESS.equals(responseMap.get("return_code")) &&
                WechatPayConstants.WECHAT_PAY_SUCCESS.equals(responseMap.get("result_code"))) {

                String refundStatus = (String) responseMap.get("refund_status_0");
                return convertWechatRefundStatusToOrderStatus(refundStatus);
            }

        } catch (Exception e) {
            log.error("查询退款状态失败，退款单号：{}", refundNo, e);
        }

        return WechatPayConstants.REFUND_STATUS_FAILED;
    }

    @Override
    @Transactional
    public boolean updateOrderPayStatus(String orderNo) {
        try {
            String payStatus = queryOrderStatus(orderNo);

            if (WechatPayConstants.ORDER_STATUS_PAID.equals(payStatus)) {
                // 查询订单并更新状态
                BsePaymentOrder paymentOrder = bsePaymentOrderService.lambdaQuery()
                    .eq(BsePaymentOrder::getOrderNo, orderNo)
                    .one();

                if (paymentOrder != null && WechatPayConstants.ORDER_STATUS_PENDING.equals(paymentOrder.getStatus())) {
                    paymentOrder.setStatus(WechatPayConstants.ORDER_STATUS_PAID);
                    paymentOrder.setPaidAt(LocalDateTime.now());
                    paymentOrder.setAmountPaid(paymentOrder.getAmountPayable());

                    return bsePaymentOrderService.updateById(paymentOrder);
                }
            }

        } catch (Exception e) {
            log.error("更新订单支付状态失败，订单号：{}", orderNo, e);
        }

        return false;
    }

    /**
     * 生成退款单号
     */
    private String generateRefundNo() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return "RF" + LocalDateTime.now().format(formatter) + IdUtil.randomUUID().substring(0, 8);
    }



    /**
     * 支付成功后更新订单
     */
    private boolean updateOrderAfterPayment(Map<String, Object> callbackData) {
        String outTradeNo = (String) callbackData.get("out_trade_no");
        BsePaymentOrder paymentOrder = bsePaymentOrderService.lambdaQuery()
            .eq(BsePaymentOrder::getOrderNo, outTradeNo)
            .one();

        if (paymentOrder == null) {
            log.error("订单不存在：{}", outTradeNo);
            return false;
        }

        // 防止重复处理
        if (WechatPayConstants.ORDER_STATUS_PAID.equals(paymentOrder.getStatus())) {
            log.info("订单已处理：{}", outTradeNo);
            return true;
        }

        paymentOrder.setStatus(WechatPayConstants.ORDER_STATUS_PAID);
        paymentOrder.setChannelOrderNo((String) callbackData.get("transaction_id"));

        // 处理金额字段，可能是String或Integer
        Object totalFeeObj = callbackData.get("total_fee");
        if (totalFeeObj != null) {
            if (totalFeeObj instanceof String) {
                paymentOrder.setAmountPaid(Integer.valueOf((String) totalFeeObj));
            } else if (totalFeeObj instanceof Integer) {
                paymentOrder.setAmountPaid((Integer) totalFeeObj);
            }
        }

        paymentOrder.setPaidAt(LocalDateTime.now());

        log.info("更新订单状态：订单号={}，微信交易号={}，金额={}",
            outTradeNo, callbackData.get("transaction_id"), totalFeeObj);

        return bsePaymentOrderService.updateById(paymentOrder);
    }

    /**
     * 调用微信退款接口
     */
    private boolean callWechatRefund(BsePaymentOrder paymentOrder, WechatRefundDTO refundDTO, String refundNo) {
        try {
            // 构建退款参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", wechatPayProperties.getAppId());
            params.put("mch_id", wechatPayProperties.getMchId());
            params.put("nonce_str", WechatPayUtil.generateNonceStr());
            params.put("out_trade_no", paymentOrder.getOrderNo());
            params.put("out_refund_no", refundNo);
            params.put("total_fee", paymentOrder.getAmountPaid());
            params.put("refund_fee", refundDTO.getRefundAmount());
            params.put("refund_desc", refundDTO.getRefundReason());

            // 生成签名
            String sign = WechatPayUtil.generateSign(params, wechatPayProperties.getApiKey());
            params.put("sign", sign);

            // 发送请求（退款接口需要证书）
            String xmlData = WechatPayUtil.mapToXml(params);
            String response = WechatPayUtil.sendPostRequest(WechatPayConstants.REFUND_URL, xmlData, sslRestTemplate);

            log.info("微信退款请求发送成功，使用SSL证书");

            // 解析响应
            Map<String, Object> responseMap = WechatPayUtil.xmlToMap(response);

            return WechatPayConstants.WECHAT_PAY_SUCCESS.equals(responseMap.get("return_code")) &&
                   WechatPayConstants.WECHAT_PAY_SUCCESS.equals(responseMap.get("result_code"));

        } catch (Exception e) {
            log.error("调用微信退款接口失败", e);
            return false;
        }
    }

    /**
     * 转换微信退款状态为订单退款状态
     */
    private String convertWechatRefundStatusToOrderStatus(String wechatRefundStatus) {
        return switch (wechatRefundStatus) {
            case "SUCCESS" -> WechatPayConstants.REFUND_STATUS_SUCCESS;
            case "REFUNDCLOSE" -> WechatPayConstants.REFUND_STATUS_FAILED;
            case "PROCESSING" -> WechatPayConstants.REFUND_STATUS_PENDING;
            default -> WechatPayConstants.REFUND_STATUS_FAILED;
        };
    }
}
