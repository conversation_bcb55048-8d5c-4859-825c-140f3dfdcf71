/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.service.impl;

import cn.hutool.core.util.IdUtil;
import com.izpan.infrastructure.config.WechatPayProperties;
import com.izpan.modules.base.domain.dto.payment.WechatPayOrderDTO;
import com.izpan.modules.base.domain.dto.payment.WechatRefundDTO;
import com.izpan.modules.base.domain.entity.BsePaymentOrder;
import com.izpan.modules.base.domain.vo.WechatPayResultVO;
import com.izpan.modules.base.service.IBsePaymentOrderService;
import com.izpan.modules.base.service.IWechatPayService;
import com.izpan.infrastructure.constants.WechatPayConstants;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付V3服务实现类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.service.impl.WechatPayV3ServiceImpl
 * @CreateTime 2025-07-21
 */
@Slf4j
@Service
@Primary
@ConditionalOnProperty(name = "wechat.pay.api-v3-key")
public class WechatPayV3ServiceImpl implements IWechatPayService {

    private final WechatPayProperties wechatPayProperties;
    private final IBsePaymentOrderService bsePaymentOrderService;

    @Autowired(required = false)
    private JsapiService jsapiService;

    @Autowired(required = false)
    private RefundService refundService;

    public WechatPayV3ServiceImpl(WechatPayProperties wechatPayProperties,
                                 IBsePaymentOrderService bsePaymentOrderService) {
        this.wechatPayProperties = wechatPayProperties;
        this.bsePaymentOrderService = bsePaymentOrderService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WechatPayResultVO createPayOrder(WechatPayOrderDTO dto) {
        try {
            log.info("开始创建微信支付V3订单，参数：{}", dto);

            // 检查V3服务是否可用
            if (jsapiService == null) {
                log.error("微信支付V3服务未配置，请检查配置文件");
                throw new RuntimeException("微信支付V3服务未配置");
            }

            // 生成商户订单号
            String outTradeNo = "WX" + System.currentTimeMillis() + IdUtil.getSnowflakeNextIdStr().substring(0, 8);

            // 构建支付请求
            PrepayRequest request = new PrepayRequest();
            
            // 设置基本信息
            request.setAppid(wechatPayProperties.getAppId());
            request.setMchid(wechatPayProperties.getMchId());
            request.setDescription(dto.getBody());
            request.setOutTradeNo(outTradeNo);
            request.setNotifyUrl(wechatPayProperties.getNotifyUrl());
            
            // 设置金额（单位：分）
            Amount amount = new Amount();
            amount.setTotal(dto.getAmountPayable());
            request.setAmount(amount);
            
            // 设置支付者信息（小程序支付必需）
            Payer payer = new Payer();
            payer.setOpenid(dto.getOpenid());
            request.setPayer(payer);

            log.info("微信支付V3请求参数：{}", request);

            // 调用微信支付API
            PrepayResponse response = jsapiService.prepay(request);
            
            log.info("微信支付V3响应：{}", response);

            // 构建返回结果
            WechatPayResultVO result = new WechatPayResultVO();
            result.setOrderNo(outTradeNo);
            result.setStatus("SUCCESS");

            // 构建小程序支付参数
            Map<String, Object> payParams = new HashMap<>();
            payParams.put("appId", wechatPayProperties.getAppId());
            payParams.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
            payParams.put("nonceStr", IdUtil.simpleUUID());
            payParams.put("package", "prepay_id=" + response.getPrepayId());
            payParams.put("signType", "RSA");
            payParams.put("prepayId", response.getPrepayId());

            result.setPayParams(payParams);

            log.info("微信支付V3订单创建成功，订单号：{}", outTradeNo);
            return result;

        } catch (Exception e) {
            log.error("创建微信支付V3订单失败", e);
            throw new RuntimeException("创建微信支付订单失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean applyRefund(WechatRefundDTO dto) {
        try {
            log.info("开始申请微信支付V3退款，参数：{}", dto);

            // 生成退款单号
            String outRefundNo = "RF" + System.currentTimeMillis() + IdUtil.getSnowflakeNextIdStr().substring(0, 8);

            // 1. 查询订单信息
            BsePaymentOrder paymentOrder = bsePaymentOrderService.getById(dto.getOrderId());
            if (paymentOrder == null) {
                log.error("订单不存在，订单ID：{}", dto.getOrderId());
                return false;
            }

            // 2. 验证订单状态
            if (!WechatPayConstants.ORDER_STATUS_PAID.equals(paymentOrder.getStatus())) {
                log.error("订单状态不允许退款，订单ID：{}，状态：{}", dto.getOrderId(), paymentOrder.getStatus());
                return false;
            }

            // 3. 验证退款金额
            if (dto.getRefundAmount() > paymentOrder.getAmountPaid()) {
                log.error("退款金额超过已支付金额，退款金额：{}，已支付金额：{}",
                    dto.getRefundAmount(), paymentOrder.getAmountPaid());
                return false;
            }

            log.info("订单信息验证通过，订单号：{}，原始金额：{}", paymentOrder.getOrderNo(), paymentOrder.getAmountPaid());

            // 4. 构建退款请求
            CreateRequest request = new CreateRequest();
            request.setOutTradeNo(paymentOrder.getOrderNo());
            request.setOutRefundNo(outRefundNo);
            request.setReason(dto.getRefundReason());
            request.setNotifyUrl(wechatPayProperties.getNotifyUrl().replace("/pay/", "/refund/"));

            // 5. 设置退款金额
            com.wechat.pay.java.service.refund.model.AmountReq refundAmount =
                new com.wechat.pay.java.service.refund.model.AmountReq();
            refundAmount.setRefund(dto.getRefundAmount().longValue());
            refundAmount.setTotal(paymentOrder.getAmountPaid().longValue());
            refundAmount.setCurrency("CNY");
            request.setAmount(refundAmount);

            log.info("微信支付V3退款请求参数：{}", request);

            // 调用微信退款API
            Refund response = refundService.create(request);

            log.info("微信支付V3退款响应：{}", response);

            if ("SUCCESS".equals(response.getStatus().name())) {
                log.info("微信支付V3退款申请成功，退款单号：{}", outRefundNo);
                return true;
            } else {
                log.warn("微信支付V3退款申请失败，状态：{}", response.getStatus());
                return false;
            }

        } catch (Exception e) {
            log.error("申请微信支付V3退款失败", e);
            return false;
        }
    }

    @Override
    public String queryOrderStatus(String orderNo) {
        try {
            log.info("查询微信支付V3订单状态：{}", orderNo);

            // 1. 先查询本地数据库中的订单状态
            BsePaymentOrder paymentOrder = bsePaymentOrderService.lambdaQuery()
                .eq(BsePaymentOrder::getOrderNo, orderNo)
                .one();

            if (paymentOrder == null) {
                log.error("本地订单不存在：{}", orderNo);
                return "NOTFOUND";
            }

            // 2. 如果本地状态已经是已支付，直接返回
            if (WechatPayConstants.ORDER_STATUS_PAID.equals(paymentOrder.getStatus())) {
                return "SUCCESS";
            }

            // 3. 使用微信支付V3 API查询订单状态
            // TODO: 实际调用微信API查询
            // Transaction transaction = jsapiService.queryOrderByOutTradeNo(orderNo);
            // String wechatStatus = transaction.getTradeState().name();

            // 暂时返回本地状态对应的微信状态
            return convertOrderStatusToWechatStatus(paymentOrder.getStatus());

        } catch (Exception e) {
            log.error("查询微信支付V3订单状态失败，订单号：{}", orderNo, e);
            return "UNKNOWN";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePayCallback(Map<String, Object> callbackData) {
        try {
            log.info("处理微信支付V3回调：{}", callbackData);

            // 1. 从回调数据中提取关键信息
            String outTradeNo = (String) callbackData.get("out_trade_no");
            String transactionId = (String) callbackData.get("transaction_id");
            String tradeState = (String) callbackData.get("trade_state");

            if (outTradeNo == null) {
                log.error("回调数据中缺少商户订单号");
                return false;
            }

            // 2. V3版本的签名验证由SDK自动处理，这里主要验证业务逻辑
            // 验证支付状态
            if (!"SUCCESS".equals(tradeState)) {
                log.warn("支付未成功，状态：{}，订单号：{}", tradeState, outTradeNo);
                return false;
            }

            // 3. 更新订单状态
            return updateOrderAfterPayment(callbackData);

        } catch (Exception e) {
            log.error("处理微信支付V3回调失败", e);
            return false;
        }
    }

    @Override
    public String queryRefundStatus(String refundNo) {
        try {
            log.info("查询微信支付V3退款状态：{}", refundNo);

            // 使用微信支付V3 API查询退款状态
            // TODO: 调用refundService.queryByOutRefundNo(refundNo)
            // 这里暂时返回模拟状态，实际应该调用微信API

            // 同时查询本地数据库中的退款状态
            // TODO: 调用本地退款服务查询状态
            log.info("查询本地退款状态，退款单号：{}", refundNo);

            // 返回退款状态：SUCCESS-退款成功，CLOSED-退款关闭，PROCESSING-退款处理中，ABNORMAL-退款异常
            return "PROCESSING"; // 暂时返回处理中状态

        } catch (Exception e) {
            log.error("查询微信支付V3退款状态失败，退款单号：{}", refundNo, e);
            return "UNKNOWN";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderPayStatus(String orderNo) {
        try {
            log.info("更新微信支付V3订单支付状态：{}", orderNo);

            // 先查询微信支付状态
            String wechatPayStatus = queryOrderStatus(orderNo);

            if ("SUCCESS".equals(wechatPayStatus)) {
                // 调用订单服务更新本地订单状态
                boolean updateResult = bsePaymentOrderService.updateOrderPayStatus(orderNo);

                if (updateResult) {
                    log.info("订单支付状态更新成功，订单号：{}", orderNo);

                    // TODO: 这里可以添加其他业务逻辑
                    // 比如：发送支付成功通知、更新用户积分、发放商品等

                    return true;
                } else {
                    log.error("本地订单状态更新失败，订单号：{}", orderNo);
                    return false;
                }
            } else {
                log.info("微信支付状态未成功，无需更新本地状态，订单号：{}，状态：{}", orderNo, wechatPayStatus);
                return false;
            }

        } catch (Exception e) {
            log.error("更新微信支付V3订单支付状态失败，订单号：{}", orderNo, e);
            return false;
        }
    }

    /**
     * 支付成功后更新订单（从V2版本迁移）
     */
    private boolean updateOrderAfterPayment(Map<String, Object> callbackData) {
        String outTradeNo = (String) callbackData.get("out_trade_no");
        BsePaymentOrder paymentOrder = bsePaymentOrderService.lambdaQuery()
            .eq(BsePaymentOrder::getOrderNo, outTradeNo)
            .one();

        if (paymentOrder == null) {
            log.error("订单不存在：{}", outTradeNo);
            return false;
        }

        // 防止重复处理
        if (WechatPayConstants.ORDER_STATUS_PAID.equals(paymentOrder.getStatus())) {
            log.info("订单已处理：{}", outTradeNo);
            return true;
        }

        paymentOrder.setStatus(WechatPayConstants.ORDER_STATUS_PAID);
        paymentOrder.setChannelOrderNo((String) callbackData.get("transaction_id"));

        // 处理金额字段，V3版本中金额通常是Long类型（分）
        Object totalFeeObj = callbackData.get("total");
        if (totalFeeObj != null) {
            if (totalFeeObj instanceof String) {
                paymentOrder.setAmountPaid(Integer.valueOf((String) totalFeeObj));
            } else if (totalFeeObj instanceof Integer) {
                paymentOrder.setAmountPaid((Integer) totalFeeObj);
            } else if (totalFeeObj instanceof Long) {
                paymentOrder.setAmountPaid(((Long) totalFeeObj).intValue());
            }
        }

        paymentOrder.setPaidAt(LocalDateTime.now());

        log.info("更新订单状态：订单号={}，微信交易号={}，金额={}",
            outTradeNo, callbackData.get("transaction_id"), totalFeeObj);

        return bsePaymentOrderService.updateById(paymentOrder);
    }

    /**
     * 转换微信支付状态为订单状态（从V2版本迁移）
     */
    private String convertWechatStatusToOrderStatus(String wechatStatus) {
        return switch (wechatStatus) {
            case "SUCCESS" -> WechatPayConstants.ORDER_STATUS_PAID;
            case "CLOSED" -> WechatPayConstants.ORDER_STATUS_CLOSED;
            case "PAYERROR" -> WechatPayConstants.ORDER_STATUS_FAILED;
            default -> WechatPayConstants.ORDER_STATUS_PENDING;
        };
    }

    /**
     * 转换微信退款状态为订单退款状态（从V2版本迁移）
     */
    private String convertWechatRefundStatusToOrderStatus(String wechatRefundStatus) {
        return switch (wechatRefundStatus) {
            case "SUCCESS" -> WechatPayConstants.REFUND_STATUS_SUCCESS;
            case "REFUNDCLOSE" -> WechatPayConstants.REFUND_STATUS_FAILED;
            case "PROCESSING" -> WechatPayConstants.REFUND_STATUS_PENDING;
            default -> WechatPayConstants.REFUND_STATUS_FAILED;
        };
    }

    /**
     * 转换本地订单状态为微信状态
     */
    private String convertOrderStatusToWechatStatus(String orderStatus) {
        return switch (orderStatus) {
            case WechatPayConstants.ORDER_STATUS_PAID -> "SUCCESS";
            case WechatPayConstants.ORDER_STATUS_CLOSED -> "CLOSED";
            case WechatPayConstants.ORDER_STATUS_FAILED -> "PAYERROR";
            case WechatPayConstants.ORDER_STATUS_PENDING -> "NOTPAY";
            default -> "NOTPAY";
        };
    }
}
