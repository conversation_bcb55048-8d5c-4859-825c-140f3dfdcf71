/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.modules.base.domain.dto.wechat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 微信支付退款DTO
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.modules.base.domain.dto.wechat.WechatPayRefundDTO
 * @CreateTime 2025-07-21
 */
@Data
@Schema(name = "WechatPayRefundDTO", description = "微信支付退款DTO")
public class WechatPayRefundDTO {

    @Schema(description = "商户订单号")
    @NotBlank(message = "商户订单号不能为空")
    private String outTradeNo;

    @Schema(description = "退款金额（单位：分）")
    @NotNull(message = "退款金额不能为空")
    @Positive(message = "退款金额必须大于0")
    private Integer refundFee;

    @Schema(description = "订单总金额（单位：分）")
    @NotNull(message = "订单总金额不能为空")
    @Positive(message = "订单总金额必须大于0")
    private Integer totalFee;

    @Schema(description = "退款原因")
    private String refundDesc;

    @Schema(description = "商户退款单号（可选，不传则自动生成）")
    private String outRefundNo;
}
