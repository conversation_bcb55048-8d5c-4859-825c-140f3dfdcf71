/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.test;

import com.izpan.modules.base.domain.dto.payment.WechatPayOrderDTO;
import com.izpan.modules.base.domain.dto.payment.WechatRefundDTO;
import com.izpan.modules.base.domain.vo.WechatPayResultVO;
import com.izpan.modules.base.service.IWechatPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付V3测试控制器
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.test.WechatPayV3TestController
 * @CreateTime 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/test/wechat-pay-v3")
@RequiredArgsConstructor
public class WechatPayV3TestController {

    private final IWechatPayService wechatPayService;

    /**
     * 测试创建支付订单
     */
    @PostMapping("/create-order")
    public Map<String, Object> testCreateOrder(@RequestBody WechatPayOrderDTO dto) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试创建微信支付V3订单：{}", dto);
            
            WechatPayResultVO payResult = wechatPayService.createPayOrder(dto);
            
            result.put("success", true);
            result.put("data", payResult);
            result.put("message", "订单创建成功");
            
        } catch (Exception e) {
            log.error("创建微信支付V3订单失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "订单创建失败");
        }
        
        return result;
    }

    /**
     * 测试查询订单状态
     */
    @GetMapping("/query-order/{orderNo}")
    public Map<String, Object> testQueryOrder(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试查询微信支付V3订单状态：{}", orderNo);
            
            String status = wechatPayService.queryOrderStatus(orderNo);
            
            result.put("success", true);
            result.put("orderNo", orderNo);
            result.put("status", status);
            result.put("message", "查询成功");
            
        } catch (Exception e) {
            log.error("查询微信支付V3订单状态失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "查询失败");
        }
        
        return result;
    }

    /**
     * 测试申请退款
     */
    @PostMapping("/apply-refund")
    public Map<String, Object> testApplyRefund(@RequestBody WechatRefundDTO dto) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试申请微信支付V3退款：{}", dto);
            
            boolean refundResult = wechatPayService.applyRefund(dto);
            
            result.put("success", refundResult);
            result.put("data", dto);
            result.put("message", refundResult ? "退款申请成功" : "退款申请失败");
            
        } catch (Exception e) {
            log.error("申请微信支付V3退款失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "退款申请失败");
        }
        
        return result;
    }

    /**
     * 测试查询退款状态
     */
    @GetMapping("/query-refund/{refundNo}")
    public Map<String, Object> testQueryRefund(@PathVariable String refundNo) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试查询微信支付V3退款状态：{}", refundNo);
            
            String status = wechatPayService.queryRefundStatus(refundNo);
            
            result.put("success", true);
            result.put("refundNo", refundNo);
            result.put("status", status);
            result.put("message", "查询成功");
            
        } catch (Exception e) {
            log.error("查询微信支付V3退款状态失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "查询失败");
        }
        
        return result;
    }

    /**
     * 测试更新订单支付状态
     */
    @PostMapping("/update-order-status/{orderNo}")
    public Map<String, Object> testUpdateOrderStatus(@PathVariable String orderNo) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试更新微信支付V3订单状态：{}", orderNo);
            
            boolean updateResult = wechatPayService.updateOrderPayStatus(orderNo);
            
            result.put("success", updateResult);
            result.put("orderNo", orderNo);
            result.put("message", updateResult ? "状态更新成功" : "状态更新失败");
            
        } catch (Exception e) {
            log.error("更新微信支付V3订单状态失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "状态更新失败");
        }
        
        return result;
    }

    /**
     * 模拟支付回调
     */
    @PostMapping("/simulate-callback")
    public Map<String, Object> testPayCallback(@RequestBody Map<String, Object> callbackData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试微信支付V3回调处理：{}", callbackData);
            
            boolean callbackResult = wechatPayService.handlePayCallback(callbackData);
            
            result.put("success", callbackResult);
            result.put("message", callbackResult ? "回调处理成功" : "回调处理失败");
            
        } catch (Exception e) {
            log.error("处理微信支付V3回调失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "回调处理失败");
        }
        
        return result;
    }
}
