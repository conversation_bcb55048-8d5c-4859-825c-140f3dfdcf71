/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.test;

import com.izpan.infrastructure.config.WechatPayProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

/**
 * SSL测试控制器
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.test.SslTestController
 * @CreateTime 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/test/ssl")
@RequiredArgsConstructor
public class SslTestController {

    private final RestTemplate restTemplate;
    private final WechatPayProperties wechatPayProperties;

    @Qualifier("sslRestTemplate")
    private final RestTemplate sslRestTemplate;

    /**
     * 测试SSL RestTemplate配置
     */
    @GetMapping("/check-config")
    public Map<String, Object> checkSslConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("检查SSL RestTemplate配置...");
            
            result.put("success", true);
            result.put("normalRestTemplate", restTemplate.getClass().getSimpleName());
            result.put("sslRestTemplate", sslRestTemplate.getClass().getSimpleName());
            result.put("isSameInstance", restTemplate == sslRestTemplate);
            result.put("normalRequestFactory", restTemplate.getRequestFactory().getClass().getSimpleName());
            result.put("sslRequestFactory", sslRestTemplate.getRequestFactory().getClass().getSimpleName());
            
            log.info("SSL配置检查结果: {}", result);
            
        } catch (Exception e) {
            log.error("SSL配置检查失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试证书加载
     */
    @GetMapping("/test-certificate")
    public Map<String, Object> testCertificate() {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("开始测试证书加载...");

            // 加载证书
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            String certPath = wechatPayProperties.getCertPath().replace("classpath:", "");
            ClassPathResource resource = new ClassPathResource(certPath);

            result.put("certPath", certPath);
            result.put("certExists", resource.exists());
            result.put("certPassword", wechatPayProperties.getCertPassword());

            if (!resource.exists()) {
                result.put("success", false);
                result.put("error", "证书文件不存在");
                return result;
            }

            try (InputStream inputStream = resource.getInputStream()) {
                keyStore.load(inputStream, wechatPayProperties.getCertPassword().toCharArray());

                // 获取证书信息
                String alias = keyStore.aliases().nextElement();
                X509Certificate cert = (X509Certificate) keyStore.getCertificate(alias);

                result.put("success", true);
                result.put("alias", alias);
                result.put("subject", cert.getSubjectDN().toString());
                result.put("issuer", cert.getIssuerDN().toString());
                result.put("serialNumber", cert.getSerialNumber().toString());
                result.put("notBefore", cert.getNotBefore().toString());
                result.put("notAfter", cert.getNotAfter().toString());

                log.info("证书加载成功: {}", result);

            }

        } catch (Exception e) {
            log.error("证书加载失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());
        }

        return result;
    }
}
