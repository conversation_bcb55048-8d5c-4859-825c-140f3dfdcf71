/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.test;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

/**
 * SSL测试控制器
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.test.SslTestController
 * @CreateTime 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/test/ssl")
@RequiredArgsConstructor
public class SslTestController {

    @Qualifier("wechatPaySslRestTemplate")
    private final RestTemplate sslRestTemplate;

    @Qualifier("wechatPayRestTemplate")
    private final RestTemplate normalRestTemplate;

    /**
     * 测试SSL证书配置
     */
    @GetMapping("/test-certificate")
    public String testSslCertificate() {
        try {
            log.info("开始测试SSL证书配置...");
            
            // 构造一个简单的XML请求
            String xmlData = "<xml><test>ssl-test</test></xml>";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_XML);
            
            HttpEntity<String> entity = new HttpEntity<>(xmlData, headers);
            
            // 尝试访问微信支付的测试接口（这会失败，但我们可以看到SSL握手是否成功）
            ResponseEntity<String> response = sslRestTemplate.postForEntity(
                "https://api.mch.weixin.qq.com/secapi/pay/refund", 
                entity, 
                String.class
            );
            
            log.info("SSL测试响应: {}", response.getBody());
            return "SSL证书配置正常";
            
        } catch (Exception e) {
            log.error("SSL证书测试失败", e);
            return "SSL证书配置失败: " + e.getMessage();
        }
    }

    /**
     * 测试普通RestTemplate
     */
    @GetMapping("/test-normal")
    public String testNormalRestTemplate() {
        try {
            log.info("开始测试普通RestTemplate...");
            
            // 构造一个简单的XML请求
            String xmlData = "<xml><test>normal-test</test></xml>";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_XML);
            
            HttpEntity<String> entity = new HttpEntity<>(xmlData, headers);
            
            // 尝试访问微信支付的普通接口
            ResponseEntity<String> response = normalRestTemplate.postForEntity(
                "https://api.mch.weixin.qq.com/pay/unifiedorder", 
                entity, 
                String.class
            );
            
            log.info("普通测试响应: {}", response.getBody());
            return "普通RestTemplate配置正常";
            
        } catch (Exception e) {
            log.error("普通RestTemplate测试失败", e);
            return "普通RestTemplate配置失败: " + e.getMessage();
        }
    }
}
