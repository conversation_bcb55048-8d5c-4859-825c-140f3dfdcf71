/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.payment;

import com.izpan.infrastructure.constants.WechatPayConstants;
import com.izpan.infrastructure.util.WechatPayUtil;
import com.izpan.modules.base.domain.dto.payment.WechatCallbackDTO;
import com.izpan.modules.base.service.IWechatPayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 微信支付回调 Controller 控制层
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.payment.WechatPayCallbackController
 * @CreateTime 2025-07-20
 */
@Slf4j
@RestController
@Tag(name = "微信支付回调")
@RequiredArgsConstructor
@RequestMapping("/wechat/pay")
public class WechatPayCallbackController {

    private final IWechatPayService wechatPayService;

    @PostMapping(value = "/notify", produces = MediaType.APPLICATION_XML_VALUE)
    @Operation(operationId = "1", summary = "微信支付结果通知")
    public String payNotify(@RequestBody String xmlData) {
        log.info("收到微信支付回调通知：{}", xmlData);
        
        try {
            // 1. 解析XML数据
            Map<String, Object> callbackMap = WechatPayUtil.xmlToMap(xmlData);
            
            // 2. 转换为DTO对象
            WechatCallbackDTO callbackData = convertMapToCallbackDTO(callbackMap);
            
            // 3. 处理回调
            boolean success = wechatPayService.handlePayCallback(callbackData);
            
            if (success) {
                log.info("微信支付回调处理成功，订单号：{}", callbackData.getOutTradeNo());
                return WechatPayConstants.WECHAT_CALLBACK_SUCCESS;
            } else {
                log.error("微信支付回调处理失败，订单号：{}", callbackData.getOutTradeNo());
                return WechatPayConstants.WECHAT_CALLBACK_FAIL;
            }
            
        } catch (Exception e) {
            log.error("处理微信支付回调异常", e);
            return WechatPayConstants.WECHAT_CALLBACK_FAIL;
        }
    }

    /**
     * 转换Map为WechatCallbackDTO
     */
    private WechatCallbackDTO convertMapToCallbackDTO(Map<String, Object> callbackMap) {
        WechatCallbackDTO callbackData = new WechatCallbackDTO();
        callbackData.setReturnCode((String) callbackMap.get("return_code"));
        callbackData.setReturnMsg((String) callbackMap.get("return_msg"));
        callbackData.setAppid((String) callbackMap.get("appid"));
        callbackData.setMchId((String) callbackMap.get("mch_id"));
        callbackData.setDeviceInfo((String) callbackMap.get("device_info"));
        callbackData.setNonceStr((String) callbackMap.get("nonce_str"));
        callbackData.setSign((String) callbackMap.get("sign"));
        callbackData.setSignType((String) callbackMap.get("sign_type"));
        callbackData.setResultCode((String) callbackMap.get("result_code"));
        callbackData.setErrCode((String) callbackMap.get("err_code"));
        callbackData.setErrCodeDes((String) callbackMap.get("err_code_des"));
        callbackData.setOpenid((String) callbackMap.get("openid"));
        callbackData.setIsSubscribe((String) callbackMap.get("is_subscribe"));
        callbackData.setTradeType((String) callbackMap.get("trade_type"));
        callbackData.setBankType((String) callbackMap.get("bank_type"));
        
        // 处理数字类型字段
        Object totalFee = callbackMap.get("total_fee");
        if (totalFee != null) {
            callbackData.setTotalFee(Integer.valueOf(totalFee.toString()));
        }
        
        Object settlementTotalFee = callbackMap.get("settlement_total_fee");
        if (settlementTotalFee != null) {
            callbackData.setSettlementTotalFee(Integer.valueOf(settlementTotalFee.toString()));
        }
        
        callbackData.setFeeType((String) callbackMap.get("fee_type"));
        
        Object cashFee = callbackMap.get("cash_fee");
        if (cashFee != null) {
            callbackData.setCashFee(Integer.valueOf(cashFee.toString()));
        }
        
        callbackData.setCashFeeType((String) callbackMap.get("cash_fee_type"));
        callbackData.setTransactionId((String) callbackMap.get("transaction_id"));
        callbackData.setOutTradeNo((String) callbackMap.get("out_trade_no"));
        callbackData.setAttach((String) callbackMap.get("attach"));
        callbackData.setTimeEnd((String) callbackMap.get("time_end"));
        
        return callbackData;
    }

}
