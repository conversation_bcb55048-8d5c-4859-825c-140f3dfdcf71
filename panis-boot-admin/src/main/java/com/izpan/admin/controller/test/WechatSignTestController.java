/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.admin.controller.test;

import cn.hutool.crypto.digest.DigestUtil;
import com.izpan.infrastructure.config.WechatPayProperties;
import com.izpan.infrastructure.util.WechatPayUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 微信签名测试控制器
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.admin.controller.test.WechatSignTestController
 * @CreateTime 2025-07-21
 */
@Slf4j
@RestController
@RequestMapping("/test/wechat-sign")
@RequiredArgsConstructor
public class WechatSignTestController {

    private final WechatPayProperties wechatPayProperties;

    /**
     * 测试微信签名生成
     */
    @GetMapping("/test-sign")
    public Map<String, Object> testWechatSign() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 模拟统一下单的参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", wechatPayProperties.getAppId());
            params.put("mch_id", wechatPayProperties.getMchId());
            params.put("nonce_str", "test123456789");
            params.put("body", "测试商品");
            params.put("out_trade_no", "TEST" + System.currentTimeMillis());
            params.put("total_fee", 1);
            params.put("spbill_create_ip", "127.0.0.1");
            params.put("notify_url", wechatPayProperties.getNotifyUrl());
            params.put("trade_type", "JSAPI");
            params.put("openid", "test_openid");
            
            // 生成签名
            String sign = WechatPayUtil.generateSign(params, wechatPayProperties.getApiKey());
            
            // 手动验证签名生成过程
            TreeMap<String, Object> sortedParams = new TreeMap<>(params);
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (value != null && !value.toString().isEmpty() && !"sign".equals(key)) {
                    sb.append(key).append("=").append(value).append("&");
                }
            }
            sb.append("key=").append(wechatPayProperties.getApiKey());
            
            String stringSignTemp = sb.toString();
            String manualSign = DigestUtil.md5Hex(stringSignTemp).toUpperCase();
            
            result.put("success", true);
            result.put("config", Map.of(
                "appId", wechatPayProperties.getAppId(),
                "mchId", wechatPayProperties.getMchId(),
                "apiKeyPrefix", wechatPayProperties.getApiKey() != null ? 
                    wechatPayProperties.getApiKey().substring(0, 4) + "****" : "null",
                "notifyUrl", wechatPayProperties.getNotifyUrl()
            ));
            result.put("params", params);
            result.put("stringSignTemp", stringSignTemp);
            result.put("utilSign", sign);
            result.put("manualSign", manualSign);
            result.put("signMatch", sign.equals(manualSign));
            
            log.info("签名测试结果: {}", result);
            
        } catch (Exception e) {
            log.error("签名测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试标准微信签名示例
     */
    @GetMapping("/test-standard-sign")
    public Map<String, Object> testStandardSign() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 使用微信官方文档的示例参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", "wxd930ea5d5a258f4f");
            params.put("mch_id", "10000100");
            params.put("device_info", "1000");
            params.put("body", "test");
            params.put("nonce_str", "ibuaiVcKdpRxkhJA");
            
            // 使用示例密钥
            String testApiKey = "192006250b4c09247ec02edce69f6a2d";
            
            String sign = WechatPayUtil.generateSign(params, testApiKey);
            
            // 预期的签名应该是：9A0A8659F005D6984697E2CA0A9CF3B7
            String expectedSign = "9A0A8659F005D6984697E2CA0A9CF3B7";
            
            result.put("success", true);
            result.put("params", params);
            result.put("apiKey", testApiKey);
            result.put("generatedSign", sign);
            result.put("expectedSign", expectedSign);
            result.put("signMatch", sign.equals(expectedSign));
            
            log.info("标准签名测试结果: {}", result);
            
        } catch (Exception e) {
            log.error("标准签名测试失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
