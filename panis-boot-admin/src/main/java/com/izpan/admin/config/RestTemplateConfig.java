package com.izpan.admin.config;

import cn.hutool.core.util.StrUtil;
import com.izpan.infrastructure.config.WechatPayProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder;
import org.apache.hc.core5.ssl.SSLContexts;
import org.apache.hc.core5.ssl.TrustStrategy;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyStore;
import java.time.Duration;

/**
 * RestTemplate配置类
 *
 * <AUTHOR>
 * @CreateTime 2024-03-02
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class RestTemplateConfig {

    private final WechatPayProperties wechatPayProperties;

    /**
     * 创建普通RestTemplate Bean（用于一般HTTP请求）
     *
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
            .setConnectTimeout(Duration.ofSeconds(60))  // 设置连接超时为60秒
            .setReadTimeout(Duration.ofSeconds(60))     // 设置读取超时为60秒
            .additionalMessageConverters(new StringHttpMessageConverter(StandardCharsets.UTF_8))
            .build();
    }

    /**
     * 创建支持SSL证书的RestTemplate Bean（用于微信退款等需要证书的接口）
     * 如果SSL配置失败，返回普通RestTemplate
     *
     * @return SSL RestTemplate实例
     */
    @Bean("sslRestTemplate")
    public RestTemplate sslRestTemplate() {
        try {
            // 如果没有配置证书路径，返回普通的RestTemplate
            if (StrUtil.isBlank(wechatPayProperties.getCertPath())) {
                log.warn("未配置微信支付证书路径，SSL RestTemplate将使用普通配置");
                return restTemplate(new RestTemplateBuilder());
            }

            log.info("开始配置SSL RestTemplate，证书路径：{}", wechatPayProperties.getCertPath());

            // 加载证书
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            String certPath = wechatPayProperties.getCertPath().replace("classpath:", "");
            ClassPathResource resource = new ClassPathResource(certPath);

            if (!resource.exists()) {
                log.warn("证书文件不存在：{}，SSL RestTemplate将使用普通配置", certPath);
                return restTemplate(new RestTemplateBuilder());
            }

            try (InputStream inputStream = resource.getInputStream()) {
                keyStore.load(inputStream, wechatPayProperties.getCertPassword().toCharArray());
                log.info("SSL证书加载成功");
            }

            // 创建SSL上下文
            SSLContext sslContext = SSLContexts.custom()
                    .loadKeyMaterial(keyStore, wechatPayProperties.getCertPassword().toCharArray())
                    .loadTrustMaterial(null, (TrustStrategy) (chain, authType) -> true)
                    .build();

            // 创建SSL连接工厂
            SSLConnectionSocketFactory sslConnectionSocketFactory = SSLConnectionSocketFactoryBuilder.create()
                    .setSslContext(sslContext)
                    .setTlsVersions("TLSv1.2", "TLSv1.3")
                    .build();

            // 创建连接管理器
            var connectionManager = PoolingHttpClientConnectionManagerBuilder.create()
                    .setSSLSocketFactory(sslConnectionSocketFactory)
                    .build();

            // 创建HttpClient
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .build();

            // 创建请求工厂
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);
            requestFactory.setConnectTimeout(60000);
            requestFactory.setConnectionRequestTimeout(60000);

            RestTemplate sslRestTemplate = new RestTemplate(requestFactory);
            sslRestTemplate.getMessageConverters().add(new StringHttpMessageConverter(StandardCharsets.UTF_8));

            log.info("SSL RestTemplate配置成功");
            return sslRestTemplate;

        } catch (Exception e) {
            log.error("配置SSL RestTemplate失败，将使用普通RestTemplate", e);
            return restTemplate(new RestTemplateBuilder());
        }
    }
}