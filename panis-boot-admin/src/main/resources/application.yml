server:
  # 服务端口
  port: 9999

spring:
  profiles:
    # 激活环境
    active: @env@
  application:
    name: panis-boot-admin
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  config:
    import:
      - classpath:config/druid.yml
      - classpath:config/mybatis-plus.yml
      - classpath:config/swagger.yml
      - classpath:config/quartz.yml
  servlet:
    multipart:
      enabled: true #默认支持文件上传
      max-file-size: 20MB # 最大支持文件大小
      max-request-size: 20MB # 最大支持请求大小