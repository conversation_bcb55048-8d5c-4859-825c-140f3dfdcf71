spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************************
    username: cewenwang
    password: JkbkMtStanRjRPdz
  #    url: ***********************************************************************************************************************************************************************************************
  #    username: root
  #    password: ita@2020
  data:
    redis:
      database: 1
      host: **************
      port: 6739
      connect-timeout: 10000ms
      lettuce:
        pool:
          enabled: true
#oss:
#  name: minio
#  bucket-name: essay-java
#  endpoint: http://**************:19000
#  access-key: P5fa3Pi7cY3PFJkaKm1V
#  secret-key: gMii9pcwXopsRNDC81ToYHUBLJOzptKJ7TefvEVx
#  sync-delete: true
#  expiry: 3600
#  image-url: https://test.zhoukaiwen.com/img-api/essay-java/
#  password: 7Fi4BRx88KaYskYw

oss:
  name: minio
  bucket-name: cww
  endpoint: http://**************:9000
  access-key: AKMBfHO0BN0HEP8T0Q7Z
  secret-key: 8H6tSwSv4C3KwNHaBePA43sZEVlcZYImE75SOHn3
  sync-delete: true
  expiry: 3600
  image-url: http://**************:9000/cww/

ceping:
  pocr: http://dmbth.cewenwang.com/interface/Ocr_Service.asmx/ocrcp
  pcp: http://dmbth.cewenwang.com/interface/Ceping_Servpice.asmx/submitTest_ceping_bzk
  ocr: https://jxlst.cewenwang.com/interface/Ocr_Service.asmx/ocrcp
  cp: https://jxlst.cewenwang.com/interface/Ceping_Servpice.asmx/submitTest_ceping_bzk
  fw: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectOne_fanwenBytitle
  sc: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectList_h
  dsc: https://jxlst.cewenwang.com/interface/Sucai_Service.asmx/selectOne_SucaiById

# 微信支付配置
wechat:
  pay:
    # 小程序AppID
    app-id: your_mini_program_app_id
    # 商户号
    mch-id: your_merchant_id
    # 商户API密钥
    api-key: your_api_key
    # 支付回调通知地址
    notify-url: http://your-domain.com/wechat/pay/notify
    # 商户证书路径（退款时需要）
    cert-path: classpath:cert/apiclient_cert.p12
    # 证书密码（默认为商户号）
    cert-password: your_merchant_id
