spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**************:3306/cewenwang?socketTimeout=60000&connectTimeout=30000&useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&allowMultiQueries=true&serverTimezone=Asia/Shanghai
    username: cewenwang
    password: JkbkMtStanRjRPdz
  #    url: ***********************************************************************************************************************************************************************************************
  #    username: root
  #    password: ita@2020
  data:
    redis:
      database: 1
      host: **************
      port: 6739
      connect-timeout: 10000ms
      lettuce:
        pool:
          enabled: true
#oss:
#  name: minio
#  bucket-name: essay-java
#  endpoint: http://**************:19000
#  access-key: P5fa3Pi7cY3PFJkaKm1V
#  secret-key: gMii9pcwXopsRNDC81ToYHUBLJOzptKJ7TefvEVx
#  sync-delete: true
#  expiry: 3600
#  image-url: https://test.zhoukaiwen.com/img-api/essay-java/
#  password: 7Fi4BRx88KaYskYw

# 微信配置
wechat:
  # 小程序配置
  miniapp:
    # 小程序AppID
    app-id: wx17adbb00eb04dbde
    # 小程序AppSecret
    app-secret: 4289e801921592e0dc813aa6646eb0e7
  # 支付配置
  pay:
    # 小程序AppID
    app-id: wx17adbb00eb04dbde
    # 商户号
    mch-id: 1457388702
    # 商户API密钥
    api-key: 51DEFCF72D4BCE13252B786063CE57E3
    # 支付回调通知地址
    notify-url: https://www.gaibiyou.com/cww-api/wechat/pay/notify
    # 商户证书路径（退款时需要）
    cert-path: classpath:cert/apiclient_cert.p12
    # 证书密码（默认为商户号）
    cert-password: 1457388702
