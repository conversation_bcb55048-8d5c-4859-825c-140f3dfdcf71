<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- SpringBoot 版本 -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.2</version>
        <relativePath/>
    </parent>

    <groupId>com.izpan</groupId>
    <artifactId>panis-boot</artifactId>
    <version>1.0.5-SNAPSHOT</version>
    <description>PanisBoot 后台管理项目</description>

    <packaging>pom</packaging>
    <modules>
        <!-- 基础模块 -->
        <module>panis-boot-common</module>
        <!-- 基础配置模块 -->
        <module>panis-boot-infrastructure</module>
        <!-- 业务模块 -->
        <module>panis-boot-modules</module>
        <!-- 后台管理 -->
        <module>panis-boot-admin</module>
    </modules>


    <!-- 资源属性 -->
    <properties>
        <!-- Java 版本 -->
        <java.version>20</java.version>
        <maven.compiler.source>20</maven.compiler.source>
        <maven.compiler.target>20</maven.compiler.target>
        <maven.plugin.version>3.13.0</maven.plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- SpringBoot -->

        <!-- 其他工具版本 -->
        <lombok.version>1.18.34</lombok.version>
        <knife4j.version>4.5.0</knife4j.version>
        <java.jwt.version>4.4.0</java.jwt.version>
        <jsr310.version>2.17.1</jsr310.version>
        <oshi.version>6.6.1</oshi.version>
        <ip2region.version>2.7.0</ip2region.version>
        <classgraph.version>4.8.177</classgraph.version>
        <snakeyaml.version>2.3</snakeyaml.version>
        <minio.version>8.5.13</minio.version>
        <poi-tl.version>1.12.2</poi-tl.version>
        <fastjson2.version>2.0.57</fastjson2.version>
        <okhttp.version>5.0.0-alpha.13</okhttp.version>
        <hutool.version>5.8.35</hutool.version>
        <httpclient5.version>5.2.1</httpclient5.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Modules -->
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-modules</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-admin</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- knife4j swagger -->
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>

            <!-- Jackson Datatype -->
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jsr310.version}</version>
            </dependency>

            <!-- 系统监控 Core -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <!-- ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <!-- 类路径扫描器 ClassGraph -->
            <dependency>
                <groupId>io.github.classgraph</groupId>
                <artifactId>classgraph</artifactId>
                <version>${classgraph.version}</version>
            </dependency>

            <!-- SnakeYAML -->
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <!-- OSS -->
            <!-- Minio -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <!-- POI-TL -->
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${poi-tl.version}</version>
            </dependency>

            <!-- Starter -->
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-code-generator</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-database-mybatis-plus</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-database-mysql</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-database-redis</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-sa-token</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-job-quartz</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-database-sharding</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-oss</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.izpan</groupId>
                <artifactId>panis-boot-starter-excel</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- fastjson2 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- Apache HttpClient 5 -->
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>

            

        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>${project.name}-${project.version}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>application*.yml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>application.yml</include>
                    <include>application-${env}.yml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>20</source>
                    <target>20</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                        <arg>-Xlint:-options</arg>
                        <arg>-J--add-opens java.base/java.lang=ALL-UNNAMED</arg>
                        <arg>-J--add-opens java.base/jdk.internal.misc=ALL-UNNAMED</arg>
                        <arg>-J-Dio.netty.tryReflectionSetAccessible=true</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!-- 配置不同的profile，对应不同的生产环境 -->
    <profiles>
        <profile>
            <!-- 开发 -->
            <id>dev</id>
            <properties>
                <env>dev</env>
                <maven.test.skip>true</maven.test.skip>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <!-- 生产 -->
            <id>prod</id>
            <properties>
                <env>prod</env>
                <maven.test.skip>true</maven.test.skip>
            </properties>
        </profile>
    </profiles>

</project>