/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.infrastructure.util;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.XML;
import com.izpan.infrastructure.constants.WechatPayConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.TreeMap;

/**
 * 微信支付工具类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.util.WechatPayUtil
 * @CreateTime 2025-07-20
 */
@Slf4j
public class WechatPayUtil {

    /**
     * 生成随机字符串
     *
     * @return 32位随机字符串
     */
    public static String generateNonceStr() {
        return RandomUtil.randomString(32);
    }

    /**
     * 生成签名
     *
     * @param params 参数Map
     * @param apiKey API密钥
     * @return 签名字符串
     */
    public static String generateSign(Map<String, Object> params, String apiKey) {
        // 使用TreeMap自动排序
        TreeMap<String, Object> sortedParams = new TreeMap<>(params);
        
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // 跳过空值和sign字段
            if (value != null && StrUtil.isNotBlank(value.toString()) && !"sign".equals(key)) {
                sb.append(key).append("=").append(value).append("&");
            }
        }
        
        // 添加API密钥
        sb.append("key=").append(apiKey);
        
        String stringSignTemp = sb.toString();
        log.debug("签名原串: {}", stringSignTemp);
        
        // MD5加密并转大写
        String sign = DigestUtil.md5Hex(stringSignTemp).toUpperCase();
        log.debug("生成签名: {}", sign);
        
        return sign;
    }

    /**
     * 验证签名
     *
     * @param params 参数Map
     * @param apiKey API密钥
     * @return 验证结果
     */
    public static boolean verifySign(Map<String, Object> params, String apiKey) {
        String sign = (String) params.get("sign");
        if (StrUtil.isBlank(sign)) {
            log.error("签名验证失败：回调数据中没有sign字段");
            return false;
        }

        String calculatedSign = generateSign(params, apiKey);
        boolean isValid = sign.equals(calculatedSign);

        if (!isValid) {
            log.error("签名验证失败：原始签名={}，计算签名={}，参数={}", sign, calculatedSign, params);
        } else {
            log.info("签名验证成功");
        }

        return isValid;
    }

    /**
     * Map转XML
     *
     * @param params 参数Map
     * @return XML字符串
     */
    public static String mapToXml(Map<String, Object> params) {
        StringBuilder xml = new StringBuilder();
        xml.append("<xml>");
        
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value != null) {
                xml.append("<").append(key).append("><![CDATA[")
                   .append(value).append("]]></").append(key).append(">");
            }
        }
        
        xml.append("</xml>");
        return xml.toString();
    }

    /**
     * XML转Map
     *
     * @param xmlStr XML字符串
     * @return 参数Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> xmlToMap(String xmlStr) {
        try {
            return XML.toJSONObject(xmlStr).getJSONObject("xml").toBean(Map.class);
        } catch (Exception e) {
            log.error("XML转Map失败: {}", xmlStr, e);
            throw new RuntimeException("XML解析失败", e);
        }
    }

    /**
     * 发送HTTP POST请求
     *
     * @param url 请求地址
     * @param xmlData XML数据
     * @param restTemplate RestTemplate实例
     * @return 响应结果
     */
    public static String sendPostRequest(String url, String xmlData, RestTemplate restTemplate) {
        try {
            log.info("发送POST请求到: {}", url);
            log.info("使用的RestTemplate类型: {}", restTemplate.getClass().getSimpleName());
            log.debug("请求数据: {}", xmlData);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_XML);
            headers.add("User-Agent", "WechatPay-Java-SDK");
            HttpEntity<String> entity = new HttpEntity<>(xmlData, headers);

            log.info("开始发送HTTP请求...");
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            String responseBody = response.getBody();
            log.info("HTTP请求成功，响应状态: {}", response.getStatusCode());
            log.debug("响应数据: {}", responseBody);

            return responseBody;
        } catch (Exception e) {
            log.error("HTTP请求失败: {}", url, e);
            // 如果是SSL相关错误，提供更详细的信息
            if (e.getMessage().contains("SSL") || e.getMessage().contains("certificate")) {
                log.error("SSL证书相关错误，请检查证书配置");
            }
            throw new RuntimeException("HTTP请求失败", e);
        }
    }

    /**
     * 生成小程序支付参数
     *
     * @param appId 小程序AppID
     * @param prepayId 预支付交易会话标识
     * @param apiKey API密钥
     * @return 小程序支付参数Map
     */
    public static Map<String, Object> generateMiniProgramPayParams(String appId, String prepayId, String apiKey) {
        Map<String, Object> params = new TreeMap<>();
        params.put("appId", appId);
        params.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("nonceStr", generateNonceStr());
        params.put("package", "prepay_id=" + prepayId);
        params.put("signType", WechatPayConstants.SIGN_TYPE_MD5);
        
        // 生成签名
        String paySign = generateSign(params, apiKey);
        params.put("paySign", paySign);
        
        return params;
    }

}
