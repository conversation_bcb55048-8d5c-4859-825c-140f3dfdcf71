/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.infrastructure.config;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.cert.X509Certificate;
import java.time.Duration;

/**
 * OkHttp配置类（可选）
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.config.OkHttpConfig
 * @CreateTime 2025-07-21
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(name = "wechat.pay.http-client", havingValue = "okhttp")
public class OkHttpConfig {

    private final WechatPayProperties wechatPayProperties;

    /**
     * 普通OkHttp客户端
     */
    @Bean("wechatPayOkHttpRestTemplate")
    public RestTemplate wechatPayOkHttpRestTemplate() {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(Duration.ofSeconds(30))
                .readTimeout(Duration.ofSeconds(30))
                .writeTimeout(Duration.ofSeconds(30))
                .build();

        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
        return new RestTemplate(factory);
    }

    /**
     * 带SSL证书的OkHttp客户端
     */
    @Bean("wechatPayOkHttpSslRestTemplate")
    public RestTemplate wechatPayOkHttpSslRestTemplate() {
        try {
            if (StrUtil.isBlank(wechatPayProperties.getCertPath())) {
                log.warn("未配置微信支付证书路径，使用普通OkHttp客户端");
                return wechatPayOkHttpRestTemplate();
            }

            // 加载证书
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            String certPath = wechatPayProperties.getCertPath().replace("classpath:", "");
            ClassPathResource resource = new ClassPathResource(certPath);
            
            try (InputStream inputStream = resource.getInputStream()) {
                keyStore.load(inputStream, wechatPayProperties.getCertPassword().toCharArray());
            }

            // 创建KeyManagerFactory
            KeyManagerFactory keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
            keyManagerFactory.init(keyStore, wechatPayProperties.getCertPassword().toCharArray());

            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {}

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {}

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
            };

            // 创建SSL上下文
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(keyManagerFactory.getKeyManagers(), trustAllCerts, new java.security.SecureRandom());

            // 创建OkHttp客户端
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0])
                    .hostnameVerifier((hostname, session) -> true)
                    .connectTimeout(Duration.ofSeconds(30))
                    .readTimeout(Duration.ofSeconds(30))
                    .writeTimeout(Duration.ofSeconds(30))
                    .build();

            OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpClient);
            RestTemplate restTemplate = new RestTemplate(factory);
            
            log.info("OkHttp SSL客户端配置成功");
            return restTemplate;

        } catch (Exception e) {
            log.error("配置OkHttp SSL客户端失败", e);
            return wechatPayOkHttpRestTemplate();
        }
    }
}
