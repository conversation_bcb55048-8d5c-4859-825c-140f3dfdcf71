/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.infrastructure.config;

import cn.hutool.core.util.StrUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.refund.RefundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

/**
 * 微信支付V3配置类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.config.WechatPayV3Config
 * @CreateTime 2025-07-21
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WechatPayV3Config {

    private final WechatPayProperties wechatPayProperties;

    /**
     * JSAPI支付服务（小程序支付）
     * 直接创建，避免Bean循环引用
     */
    @Bean
    @ConditionalOnProperty(name = "wechat.pay.api-v3-key")
    public JsapiService jsapiService() {
        try {
            log.info("创建微信支付V3 JsapiService...");
            Config config = createWechatPayConfig();
            JsapiService service = new JsapiService.Builder().config(config).build();
            log.info("微信支付V3 JsapiService创建成功");
            return service;
        } catch (Exception e) {
            log.error("创建JsapiService失败", e);
            throw new RuntimeException("创建JsapiService失败: " + e.getMessage(), e);
        }
    }

    /**
     * 退款服务
     * 直接创建，避免Bean循环引用
     */
    @Bean
    @ConditionalOnProperty(name = "wechat.pay.api-v3-key")
    public RefundService refundService() {
        try {
            log.info("创建微信支付V3 RefundService...");
            Config config = createWechatPayConfig();
            RefundService service = new RefundService.Builder().config(config).build();
            log.info("微信支付V3 RefundService创建成功");
            return service;
        } catch (Exception e) {
            log.error("创建RefundService失败", e);
            throw new RuntimeException("创建RefundService失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建微信支付配置的私有方法
     */
    private Config createWechatPayConfig() throws Exception {
        log.info("开始创建微信支付V3配置...");

        // 检查必需的配置，如果是占位符则跳过V3配置
        if (StrUtil.isBlank(wechatPayProperties.getMchId())) {
            throw new IllegalArgumentException("商户号不能为空");
        }
        if (StrUtil.isBlank(wechatPayProperties.getPrivateKeyPath()) ||
            "classpath:cert/apiclient_key.pem".equals(wechatPayProperties.getPrivateKeyPath())) {
            log.warn("商户私钥路径未配置或为默认值，跳过V3配置");
            throw new IllegalArgumentException("商户私钥路径未配置");
        }
        if (StrUtil.isBlank(wechatPayProperties.getMerchantSerialNumber()) ||
            "YOUR_MERCHANT_SERIAL_NUMBER".equals(wechatPayProperties.getMerchantSerialNumber())) {
            log.warn("商户证书序列号未配置或为默认值，跳过V3配置");
            throw new IllegalArgumentException("商户证书序列号未配置");
        }
        if (StrUtil.isBlank(wechatPayProperties.getApiV3Key()) ||
            "YOUR_API_V3_KEY".equals(wechatPayProperties.getApiV3Key())) {
            log.warn("APIV3密钥未配置或为默认值，跳过V3配置");
            throw new IllegalArgumentException("APIV3密钥未配置");
        }

        // 处理私钥路径
        String privateKeyPath = wechatPayProperties.getPrivateKeyPath();
        if (privateKeyPath.startsWith("classpath:")) {
            // 如果是classpath路径，转换为实际文件路径
            String resourcePath = privateKeyPath.replace("classpath:", "");
            ClassPathResource resource = new ClassPathResource(resourcePath);
            if (!resource.exists()) {
                throw new IllegalArgumentException("私钥文件不存在: " + resourcePath);
            }
            privateKeyPath = resource.getFile().getAbsolutePath();
        }

        // 使用自动更新平台证书的RSA配置
        Config config = new RSAAutoCertificateConfig.Builder()
                .merchantId(wechatPayProperties.getMchId())
                .privateKeyFromPath(privateKeyPath)
                .merchantSerialNumber(wechatPayProperties.getMerchantSerialNumber())
                .apiV3Key(wechatPayProperties.getApiV3Key())
                .build();

        log.info("微信支付V3配置创建成功");
        log.info("商户号: {}", wechatPayProperties.getMchId());
        log.info("证书序列号: {}", wechatPayProperties.getMerchantSerialNumber());
        log.info("私钥路径: {}", privateKeyPath);

        return config;
    }
}
