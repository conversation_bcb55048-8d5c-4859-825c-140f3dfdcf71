/*
 * All Rights Reserved: Copyright [2024] [<PERSON><PERSON> (<EMAIL>)]
 * Open Source Agreement: Apache License, Version 2.0
 * For educational purposes only, commercial use shall comply with the author's copyright information.
 * The author does not guarantee or assume any responsibility for the risks of using software.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.izpan.infrastructure.config;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactoryBuilder;
import org.apache.hc.core5.ssl.SSLContexts;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.io.InputStream;
import java.security.KeyStore;

/**
 * 微信支付配置类
 *
 * <AUTHOR>
 * @ProjectName panis-boot
 * @ClassName com.izpan.infrastructure.config.WechatPayConfig
 * @CreateTime 2025-07-20
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WechatPayConfig {

    private final WechatPayProperties wechatPayProperties;

    /**
     * 普通RestTemplate（用于统一下单、查询等不需要证书的接口）
     */
    @Bean("wechatPayRestTemplate")
    @Primary
    public RestTemplate wechatPayRestTemplate() {
        return new RestTemplate();
    }

    /**
     * 带SSL证书的RestTemplate（用于退款等需要证书的接口）
     */
    @Bean("wechatPaySslRestTemplate")
    public RestTemplate wechatPaySslRestTemplate() {
        try {
            // 如果没有配置证书路径，返回普通的RestTemplate
            if (StrUtil.isBlank(wechatPayProperties.getCertPath())) {
                log.warn("未配置微信支付证书路径，退款功能将不可用");
                return new RestTemplate();
            }

            // 加载证书
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            ClassPathResource resource = new ClassPathResource(wechatPayProperties.getCertPath().replace("classpath:", ""));
            
            try (InputStream inputStream = resource.getInputStream()) {
                keyStore.load(inputStream, wechatPayProperties.getCertPassword().toCharArray());
            }

            // 创建SSL上下文
            SSLContext sslContext = SSLContexts.custom()
                    .loadKeyMaterial(keyStore, wechatPayProperties.getCertPassword().toCharArray())
                    .build();

            // 创建SSL连接工厂
            var sslConnectionSocketFactory = SSLConnectionSocketFactoryBuilder.create()
                    .setSslContext(sslContext)
                    .build();

            // 创建连接管理器
            var connectionManager = PoolingHttpClientConnectionManagerBuilder.create()
                    .setSSLSocketFactory(sslConnectionSocketFactory)
                    .build();

            // 创建HttpClient
            CloseableHttpClient httpClient = HttpClients.custom()
                    .setConnectionManager(connectionManager)
                    .build();

            // 创建请求工厂
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);
            requestFactory.setConnectTimeout(10000);
            requestFactory.setConnectionRequestTimeout(10000);

            RestTemplate restTemplate = new RestTemplate(requestFactory);
            log.info("微信支付SSL证书配置成功");
            return restTemplate;

        } catch (Exception e) {
            log.error("配置微信支付SSL证书失败", e);
            // 如果证书配置失败，返回普通的RestTemplate
            return new RestTemplate();
        }
    }

}
